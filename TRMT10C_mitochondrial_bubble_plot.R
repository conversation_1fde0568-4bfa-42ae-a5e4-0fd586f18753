# TRMT10C与线粒体基因相关性气泡图
# 作者: AI Assistant
# 日期: 2025-07-08

# 加载必要的包
library(tidyverse)
library(ggplot2)
library(dplyr)
library(readr)
library(RColorBrewer)
library(scales)
library(ggrepel)

# 设置工作目录和文件路径
setwd(".")

# 读取数据
data_file <- "TCGA_GDC_503 Samples/mRNATPM vs TRMT10CmRNA (TCGA, GDC).tsv"
correlation_data <- read_tsv(data_file)

# 读取线粒体基因列表
mito_genes <- read_lines("能量代谢相关基因.txt")
mito_genes <- mito_genes[mito_genes != "" & mito_genes != "Symbol"]

# 线粒体基因功能分类
mito_classification <- list(
  "呼吸链复合体I" = c("NDUFS1", "NDUFS2", "NDUFS3", "NDUFS4", "NDUFS5", "NDUFS6", "NDUFS7", "NDUFS8", 
                    "NDUFV1", "NDUFV2", "NDUFV3", "NDUFA1", "NDUFA2", "NDUFA3", "NDUFA4", "NDUFA5", 
                    "NDUFA6", "NDUFA7", "NDUFA8", "NDUFA9", "NDUFA10", "NDUFA11", "NDUFA12", "NDUFA13",
                    "NDUFB1", "NDUFB2", "NDUFB3", "NDUFB4", "NDUFB5", "NDUFB6", "NDUFB7", "NDUFB8", 
                    "NDUFB9", "NDUFB10", "NDUFB11", "NDUFC1", "NDUFC2", "NDUFAB1", "MT-ND1", "MT-ND2", 
                    "MT-ND3", "MT-ND4", "MT-ND4L", "MT-ND5", "MT-ND6"),
  
  "呼吸链复合体II" = c("SDHA", "SDHB", "SDHC", "SDHD", "SDHAF1", "SDHAF2", "SDHAF3", "SDHAF4"),
  
  "呼吸链复合体III" = c("CYC1", "UQCRC1", "UQCRC2", "UQCRFS1", "UQCRH", "UQCRQ", "UQCR10", "UQCR11", 
                     "UQCRB", "MT-CYB"),
  
  "呼吸链复合体IV" = c("COX5A", "COX5B", "COX6A1", "COX6A2", "COX6B1", "COX6B2", "COX6C", "COX7A1", 
                     "COX7A2", "COX7A2L", "COX7B", "COX7B2", "COX7C", "COX8A", "COX8C", "COX4I1", 
                     "COX4I2", "COX10", "COX11", "COX14", "COX15", "COX16", "COX17", "COX18", "COX19", 
                     "COX20", "MT-CO1", "MT-CO2", "MT-CO3"),
  
  "ATP合酶复合体V" = c("ATP5F1A", "ATP5F1B", "ATP5F1C", "ATP5F1D", "ATP5F1E", "ATP5PO", "ATP5PB", 
                     "ATP5PD", "ATP5PF", "ATP5ME", "ATP5MG", "ATP5MC1", "ATP5MC2", "ATP5MC3", 
                     "ATP5MD", "ATP5MF", "ATP5MPL", "ATP5IF1", "MT-ATP6", "MT-ATP8"),
  
  "线粒体核糖体" = c("MRPL1", "MRPL2", "MRPL3", "MRPL4", "MRPL9", "MRPL10", "MRPL11", "MRPL12", 
                   "MRPL13", "MRPL14", "MRPL15", "MRPL16", "MRPL17", "MRPL18", "MRPL19", "MRPL20", 
                   "MRPL21", "MRPL22", "MRPL23", "MRPL24", "MRPL27", "MRPL28", "MRPL30", "MRPL32", 
                   "MRPL33", "MRPL34", "MRPL35", "MRPL36", "MRPL37", "MRPL38", "MRPL39", "MRPL40", 
                   "MRPL41", "MRPL42", "MRPL43", "MRPL44", "MRPL45", "MRPL46", "MRPL47", "MRPL48", 
                   "MRPL49", "MRPL50", "MRPL51", "MRPL52", "MRPL53", "MRPL54", "MRPL55", "MRPL57", 
                   "MRPL58", "MRPS2", "MRPS5", "MRPS6", "MRPS7", "MRPS9", "MRPS10", "MRPS11", 
                   "MRPS12", "MRPS14", "MRPS15", "MRPS16", "MRPS17", "MRPS18A", "MRPS18B", "MRPS18C", 
                   "MRPS21", "MRPS22", "MRPS23", "MRPS24", "MRPS25", "MRPS26", "MRPS27", "MRPS28", 
                   "MRPS30", "MRPS31", "MRPS33", "MRPS34", "MRPS35", "MRPS36"),
  
  "柠檬酸循环" = c("CS", "ACO2", "IDH3A", "IDH3B", "IDH3G", "OGDH", "DLST", "SUCLA2", "SUCLG1", 
                 "SUCLG2", "FH", "MDH2", "IDH2"),
  
  "脂肪酸氧化" = c("CPT1A", "CPT1B", "CPT1C", "CPT2", "ACADS", "ACADM", "ACADL", "ACADVL", "ACAD8", 
                 "ACAD9", "ACAD10", "ACAD11", "ETFA", "ETFB", "ETFDH", "HADHA", "HADHB", "HADH", 
                 "ECHS1", "ECHDC1", "ECHDC2", "ECHDC3"),
  
  "线粒体转运" = c("SLC25A1", "SLC25A3", "SLC25A4", "SLC25A5", "SLC25A6", "SLC25A10", "SLC25A11", 
                 "SLC25A12", "SLC25A13", "SLC25A14", "SLC25A15", "SLC25A16", "SLC25A18", "SLC25A19", 
                 "SLC25A20", "SLC25A21", "SLC25A22", "SLC25A23", "SLC25A24", "SLC25A25", "SLC25A26", 
                 "SLC25A27", "SLC25A28", "SLC25A29", "SLC25A30", "SLC25A31", "SLC25A32", "SLC25A33", 
                 "SLC25A34", "SLC25A35", "SLC25A36", "SLC25A37", "SLC25A38", "SLC25A39", "SLC25A40", 
                 "SLC25A41", "SLC25A42", "SLC25A43", "SLC25A44", "SLC25A45", "SLC25A46", "SLC25A47", 
                 "SLC25A48", "SLC25A51", "SLC25A52", "SLC25A53"),
  
  "线粒体蛋白导入" = c("TOMM5", "TOMM6", "TOMM7", "TOMM20", "TOMM22", "TOMM34", "TOMM40", "TOMM40L", 
                    "TOMM70", "TIMM8A", "TIMM8B", "TIMM9", "TIMM10", "TIMM10B", "TIMM13", "TIMM17A", 
                    "TIMM17B", "TIMM21", "TIMM22", "TIMM23", "TIMM29", "TIMM44", "TIMM50"),
  
  "其他代谢酶" = c("ACAT1", "ALAS1", "ALAS2", "BCAT2", "GLUD1", "GLUD2", "GOT2", "GPT2", "PRDX3", 
                 "PRDX5", "SOD2", "GSR", "TXN2", "TXNRD2")
)

# 为每个基因分配功能类别
assign_function <- function(gene) {
  for (category in names(mito_classification)) {
    if (gene %in% mito_classification[[category]]) {
      return(category)
    }
  }
  return("其他")
}

# 数据处理
processed_data <- correlation_data %>%
  # 筛选线粒体基因
  filter(`Correlated Gene` %in% mito_genes) %>%
  # 添加功能分类
  mutate(
    Function_Category = sapply(`Correlated Gene`, assign_function),
    # 计算显著性标记
    Significance = case_when(
      `q-Value` < 0.001 ~ "***",
      `q-Value` < 0.01 ~ "**", 
      `q-Value` < 0.05 ~ "*",
      TRUE ~ ""
    ),
    # 计算-log10(p-value)用于气泡大小
    neg_log_p = -log10(`p-Value`),
    # 相关性方向
    Correlation_Direction = ifelse(`Spearman's Correlation` > 0, "正相关", "负相关"),
    # 绝对相关系数用于筛选
    abs_correlation = abs(`Spearman's Correlation`)
  ) %>%
  # 只保留显著相关的基因 (q < 0.05)
  filter(`q-Value` < 0.05) %>%
  # 按绝对相关系数排序
  arrange(desc(abs_correlation))

# 查看数据概况
cat("处理后的数据概况:\n")
cat("总基因数:", nrow(processed_data), "\n")
cat("功能分类分布:\n")
print(table(processed_data$Function_Category))
cat("\n显著性分布:\n")
print(table(processed_data$Significance))

# 为了避免标签重叠，只标记前20个最相关的基因
top_genes <- processed_data %>%
  slice_head(n = 20) %>%
  pull(`Correlated Gene`)

processed_data <- processed_data %>%
  mutate(
    label = ifelse(`Correlated Gene` %in% top_genes, `Correlated Gene`, "")
  )

print("数据处理完成，准备创建气泡图...")

# 创建气泡图
bubble_plot <- ggplot(processed_data, aes(x = `Spearman's Correlation`,
                                         y = Function_Category,
                                         size = neg_log_p,
                                         color = Correlation_Direction)) +
  # 添加气泡
  geom_point(alpha = 0.7, stroke = 0.5) +

  # 添加基因标签（只标记前20个）
  geom_text_repel(aes(label = label),
                  size = 3,
                  max.overlaps = 20,
                  box.padding = 0.3,
                  point.padding = 0.3,
                  segment.color = "grey50",
                  segment.size = 0.3,
                  show.legend = FALSE) +

  # 添加显著性标记
  geom_text(aes(label = Significance),
            size = 4,
            vjust = -0.5,
            hjust = 0.5,
            show.legend = FALSE,
            color = "black") +

  # 设置颜色
  scale_color_manual(values = c("正相关" = "#E31A1C", "负相关" = "#1F78B4"),
                     name = "相关性方向") +

  # 设置气泡大小
  scale_size_continuous(range = c(2, 12),
                       name = "-log10(p-value)",
                       breaks = c(5, 10, 20, 50, 100),
                       labels = c("5", "10", "20", "50", "100")) +

  # 添加垂直参考线
  geom_vline(xintercept = 0, linetype = "dashed", color = "grey50", alpha = 0.7) +
  geom_vline(xintercept = c(-0.3, 0.3), linetype = "dotted", color = "grey70", alpha = 0.5) +

  # 设置坐标轴
  scale_x_continuous(limits = c(-0.8, 0.8),
                    breaks = seq(-0.8, 0.8, 0.2),
                    labels = seq(-0.8, 0.8, 0.2)) +

  # 设置主题
  theme_minimal() +
  theme(
    panel.grid.major.y = element_line(color = "grey90", size = 0.5),
    panel.grid.minor = element_blank(),
    panel.grid.major.x = element_line(color = "grey95", size = 0.3),
    axis.text.y = element_text(size = 11, color = "black"),
    axis.text.x = element_text(size = 10, color = "black"),
    axis.title = element_text(size = 12, color = "black", face = "bold"),
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 11, hjust = 0.5, color = "grey30"),
    legend.position = "bottom",
    legend.box = "horizontal",
    legend.title = element_text(size = 10, face = "bold"),
    legend.text = element_text(size = 9),
    plot.margin = margin(20, 20, 20, 20)
  ) +

  # 设置标签
  labs(
    title = "TRMT10C与线粒体基因的相关性分析",
    subtitle = paste0("数据来源: TCGA GDC (n=503), 显著相关基因数: ", nrow(processed_data)),
    x = "Spearman相关系数",
    y = "线粒体功能分类",
    caption = "气泡大小表示统计显著性 (-log10 p-value)\n显著性标记: *** p<0.001, ** p<0.01, * p<0.05"
  )

# 显示图形
print(bubble_plot)

# 保存图形
ggsave("TRMT10C_mitochondrial_correlation_bubble_plot.png",
       plot = bubble_plot,
       width = 14, height = 10,
       dpi = 300,
       bg = "white")

ggsave("TRMT10C_mitochondrial_correlation_bubble_plot.pdf",
       plot = bubble_plot,
       width = 14, height = 10,
       bg = "white")

cat("\n图形已保存为:\n")
cat("- TRMT10C_mitochondrial_correlation_bubble_plot.png\n")
cat("- TRMT10C_mitochondrial_correlation_bubble_plot.pdf\n")

# 生成详细的统计报告
cat("\n=== TRMT10C与线粒体基因相关性分析报告 ===\n")
cat("数据集: TCGA GDC (503个样本)\n")
cat("分析日期:", Sys.Date(), "\n\n")

# 总体统计
cat("1. 总体统计:\n")
cat("   - 总线粒体基因数:", length(mito_genes), "\n")
cat("   - 显著相关基因数:", nrow(processed_data), "\n")
cat("   - 显著相关比例:", round(nrow(processed_data)/length(mito_genes)*100, 1), "%\n\n")

# 相关性方向统计
direction_stats <- processed_data %>%
  group_by(Correlation_Direction) %>%
  summarise(
    count = n(),
    mean_correlation = round(mean(`Spearman's Correlation`), 3),
    median_correlation = round(median(`Spearman's Correlation`), 3),
    .groups = 'drop'
  )

cat("2. 相关性方向分布:\n")
for(i in 1:nrow(direction_stats)) {
  cat("   -", direction_stats$Correlation_Direction[i], ":",
      direction_stats$count[i], "个基因",
      "(平均相关系数:", direction_stats$mean_correlation[i], ")\n")
}

# 功能分类统计
cat("\n3. 功能分类统计:\n")
function_stats <- processed_data %>%
  group_by(Function_Category) %>%
  summarise(
    count = n(),
    mean_correlation = round(mean(abs(`Spearman's Correlation`)), 3),
    max_correlation = round(max(abs(`Spearman's Correlation`)), 3),
    .groups = 'drop'
  ) %>%
  arrange(desc(count))

for(i in 1:nrow(function_stats)) {
  cat("   -", function_stats$Function_Category[i], ":",
      function_stats$count[i], "个基因",
      "(平均|相关系数|:", function_stats$mean_correlation[i], ")\n")
}

# 显著性水平统计
cat("\n4. 显著性水平分布:\n")
sig_stats <- processed_data %>%
  group_by(Significance) %>%
  summarise(count = n(), .groups = 'drop') %>%
  arrange(desc(count))

for(i in 1:nrow(sig_stats)) {
  level <- ifelse(sig_stats$Significance[i] == "***", "p < 0.001",
                 ifelse(sig_stats$Significance[i] == "**", "p < 0.01", "p < 0.05"))
  cat("   -", level, ":", sig_stats$count[i], "个基因\n")
}

# 前10个最相关的基因
cat("\n5. 前10个最相关的基因:\n")
top10 <- processed_data %>%
  slice_head(n = 10) %>%
  select(`Correlated Gene`, Function_Category, `Spearman's Correlation`, `q-Value`)

for(i in 1:nrow(top10)) {
  cat("   ", i, ".", top10$`Correlated Gene`[i],
      "(", top10$Function_Category[i], ")",
      "- 相关系数:", round(top10$`Spearman's Correlation`[i], 3),
      ", q-value:", format(top10$`q-Value`[i], scientific = TRUE, digits = 2), "\n")
}

# 保存详细结果到文件
write_csv(processed_data, "TRMT10C_mitochondrial_correlation_results.csv")
cat("\n详细结果已保存到: TRMT10C_mitochondrial_correlation_results.csv\n")

cat("\n=== 分析完成 ===\n")
