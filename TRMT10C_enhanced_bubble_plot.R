# TRMT10C与线粒体基因相关性气泡图 - 增强版
# 作者: AI Assistant
# 日期: 2025-07-08

# 加载必要的包
library(tidyverse)
library(ggplot2)
library(dplyr)
library(readr)
library(RColorBrewer)
library(scales)
library(ggrepel)
library(viridis)

# 设置工作目录和文件路径
setwd(".")

# 读取已处理的数据
processed_data <- read_csv("TRMT10C_mitochondrial_correlation_results.csv")

# 创建增强版气泡图
enhanced_bubble_plot <- processed_data %>%
  # 按功能分类重新排序，将重要的分类放在上方
  mutate(
    Function_Category = factor(Function_Category, levels = c(
      "呼吸链复合体I", "呼吸链复合体II", "呼吸链复合体III", "呼吸链复合体IV",
      "ATP合酶复合体V", "柠檬酸循环", "脂肪酸氧化", "线粒体核糖体",
      "线粒体蛋白导入", "线粒体转运", "其他代谢酶", "其他"
    )),
    # 为不同显著性水平设置不同的透明度
    alpha_level = case_when(
      Significance == "***" ~ 0.8,
      Significance == "**" ~ 0.7,
      Significance == "*" ~ 0.6,
      TRUE ~ 0.5
    )
  ) %>%
  ggplot(aes(x = `Spearman's Correlation`, 
             y = Function_Category,
             size = neg_log_p,
             color = Correlation_Direction,
             alpha = alpha_level)) +
  
  # 添加背景网格
  geom_vline(xintercept = 0, linetype = "solid", color = "grey40", size = 0.8) +
  geom_vline(xintercept = c(-0.2, 0.2), linetype = "dashed", color = "grey60", size = 0.5) +
  geom_vline(xintercept = c(-0.4, 0.4), linetype = "dotted", color = "grey70", size = 0.3) +
  
  # 添加气泡
  geom_point(stroke = 0.3) +
  
  # 添加基因标签（只标记前15个最相关的）
  geom_text_repel(
    data = . %>% slice_head(n = 15),
    aes(label = `Correlated Gene`),
    size = 3.5,
    max.overlaps = 15,
    box.padding = 0.4,
    point.padding = 0.3,
    segment.color = "grey40",
    segment.size = 0.4,
    show.legend = FALSE,
    fontface = "bold"
  ) +
  
  # 添加显著性标记（只对前30个基因）
  geom_text(
    data = . %>% slice_head(n = 30),
    aes(label = Significance),
    size = 3.5,
    vjust = -1.2,
    hjust = 0.5,
    show.legend = FALSE,
    color = "black",
    fontface = "bold"
  ) +
  
  # 设置颜色 - 使用更鲜明的颜色
  scale_color_manual(
    values = c("正相关" = "#D73027", "负相关" = "#4575B4"),
    name = "相关性方向"
  ) +
  
  # 设置透明度
  scale_alpha_identity() +
  
  # 设置气泡大小
  scale_size_continuous(
    range = c(1.5, 15),
    name = "-log₁₀(p-value)",
    breaks = c(10, 30, 50, 80, 100),
    labels = c("10", "30", "50", "80", "100+"),
    guide = guide_legend(
      override.aes = list(alpha = 0.8),
      title.position = "top",
      title.hjust = 0.5
    )
  ) +
  
  # 设置坐标轴
  scale_x_continuous(
    limits = c(-0.6, 0.8),
    breaks = seq(-0.6, 0.8, 0.2),
    labels = seq(-0.6, 0.8, 0.2),
    expand = c(0.02, 0.02)
  ) +
  
  # 设置Y轴标签
  scale_y_discrete(
    labels = function(x) str_wrap(x, width = 12)
  ) +
  
  # 设置主题
  theme_minimal() +
  theme(
    # 面板设置
    panel.grid.major.y = element_line(color = "grey92", size = 0.6),
    panel.grid.minor = element_blank(),
    panel.grid.major.x = element_line(color = "grey96", size = 0.4),
    panel.background = element_rect(fill = "white", color = NA),
    plot.background = element_rect(fill = "white", color = NA),
    
    # 坐标轴设置
    axis.text.y = element_text(size = 11, color = "black", face = "bold"),
    axis.text.x = element_text(size = 11, color = "black"),
    axis.title = element_text(size = 13, color = "black", face = "bold"),
    axis.line.x = element_line(color = "grey30", size = 0.5),
    
    # 标题设置
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5, margin = margin(b = 10)),
    plot.subtitle = element_text(size = 12, hjust = 0.5, color = "grey20", margin = margin(b = 15)),
    plot.caption = element_text(size = 10, color = "grey40", hjust = 0, margin = margin(t = 15)),
    
    # 图例设置
    legend.position = "bottom",
    legend.box = "horizontal",
    legend.title = element_text(size = 11, face = "bold"),
    legend.text = element_text(size = 10),
    legend.key = element_blank(),
    legend.spacing.x = unit(1, "cm"),
    
    # 边距设置
    plot.margin = margin(25, 25, 25, 25)
  ) +
  
  # 设置标签
  labs(
    title = "TRMT10C与线粒体基因的相关性分析",
    subtitle = paste0("数据来源: TCGA GDC (n=503) | 显著相关基因: ", nrow(processed_data), "/1136 (75.5%)"),
    x = "Spearman相关系数",
    y = "线粒体功能分类",
    caption = "气泡大小 = 统计显著性 (-log₁₀ p-value) | 显著性标记: *** p<0.001, ** p<0.01, * p<0.05\n标注基因为相关性最强的前15个基因"
  )

# 显示图形
print(enhanced_bubble_plot)

# 保存高质量图形
ggsave("TRMT10C_enhanced_bubble_plot.png", 
       plot = enhanced_bubble_plot,
       width = 16, height = 12, 
       dpi = 300, 
       bg = "white")

ggsave("TRMT10C_enhanced_bubble_plot.pdf", 
       plot = enhanced_bubble_plot,
       width = 16, height = 12, 
       bg = "white")

cat("增强版气泡图已保存:\n")
cat("- TRMT10C_enhanced_bubble_plot.png\n")
cat("- TRMT10C_enhanced_bubble_plot.pdf\n")
