# TRMT10C与线粒体蛋白相关性气泡图
# 作者: AI Assistant
# 日期: 2025-07-08

# 加载必要的包
library(tidyverse)
library(ggplot2)
library(dplyr)
library(readr)
library(RColorBrewer)
library(scales)
library(ggrepel)
library(viridis)
library(patchwork)

# 设置工作目录
setwd(".")

# 定义蛋白表达数据集
protein_datasets <- list(
  "CPTAC_Protein" = list(
    file = "CPTAC_Cell 2021_80 Samples/蛋白丰度vs TRMT10C蛋白丰度 (CPTAC, Cell 2021).tsv",
    name = "CPTAC Cell 2021 (蛋白-蛋白)",
    samples = 80,
    type = "蛋白质组学"
  ),
  "TCGA_RPPA_Legacy" = list(
    file = "TCGA_Firehose Legacy_511 Samples/蛋白表达RPPA vs TRMT10CmRNA (TCGA, Firehose Legacy).tsv",
    name = "TCGA Firehose (RPPA)",
    samples = 511,
    type = "RPPA"
  ),
  "TCGA_RPPA_PanCancer" = list(
    file = "TCGA_PanCancer Atlas 487 Samples/蛋白表达RPPA vs TRMT10CmRNA (TCGA, PanCancer Atlas).tsv",
    name = "TCGA PanCancer (RPPA)",
    samples = 487,
    type = "RPPA"
  )
)

# 读取线粒体基因列表
mito_genes <- read_lines("能量代谢相关基因.txt")
mito_genes <- mito_genes[mito_genes != "" & mito_genes != "Symbol"]

# 线粒体基因功能分类（与RNA分析保持一致）
mito_classification <- list(
  "呼吸链复合体I" = c("NDUFS1", "NDUFS2", "NDUFS3", "NDUFS4", "NDUFS5", "NDUFS6", "NDUFS7", "NDUFS8", 
                    "NDUFV1", "NDUFV2", "NDUFV3", "NDUFA1", "NDUFA2", "NDUFA3", "NDUFA4", "NDUFA5", 
                    "NDUFA6", "NDUFA7", "NDUFA8", "NDUFA9", "NDUFA10", "NDUFA11", "NDUFA12", "NDUFA13",
                    "NDUFB1", "NDUFB2", "NDUFB3", "NDUFB4", "NDUFB5", "NDUFB6", "NDUFB7", "NDUFB8", 
                    "NDUFB9", "NDUFB10", "NDUFB11", "NDUFC1", "NDUFC2", "NDUFAB1", "MT-ND1", "MT-ND2", 
                    "MT-ND3", "MT-ND4", "MT-ND4L", "MT-ND5", "MT-ND6"),
  
  "呼吸链复合体II" = c("SDHA", "SDHB", "SDHC", "SDHD", "SDHAF1", "SDHAF2", "SDHAF3", "SDHAF4"),
  
  "呼吸链复合体III" = c("CYC1", "UQCRC1", "UQCRC2", "UQCRFS1", "UQCRH", "UQCRQ", "UQCR10", "UQCR11", 
                     "UQCRB", "MT-CYB"),
  
  "呼吸链复合体IV" = c("COX5A", "COX5B", "COX6A1", "COX6A2", "COX6B1", "COX6B2", "COX6C", "COX7A1", 
                     "COX7A2", "COX7A2L", "COX7B", "COX7B2", "COX7C", "COX8A", "COX8C", "COX4I1", 
                     "COX4I2", "COX10", "COX11", "COX14", "COX15", "COX16", "COX17", "COX18", "COX19", 
                     "COX20", "MT-CO1", "MT-CO2", "MT-CO3"),
  
  "ATP合酶复合体V" = c("ATP5F1A", "ATP5F1B", "ATP5F1C", "ATP5F1D", "ATP5F1E", "ATP5PO", "ATP5PB", 
                     "ATP5PD", "ATP5PF", "ATP5ME", "ATP5MG", "ATP5MC1", "ATP5MC2", "ATP5MC3", 
                     "ATP5MD", "ATP5MF", "ATP5MPL", "ATP5IF1", "MT-ATP6", "MT-ATP8"),
  
  "线粒体核糖体" = c("MRPL1", "MRPL2", "MRPL3", "MRPL4", "MRPL9", "MRPL10", "MRPL11", "MRPL12", 
                   "MRPL13", "MRPL14", "MRPL15", "MRPL16", "MRPL17", "MRPL18", "MRPL19", "MRPL20", 
                   "MRPL21", "MRPL22", "MRPL23", "MRPL24", "MRPL27", "MRPL28", "MRPL30", "MRPL32", 
                   "MRPL33", "MRPL34", "MRPL35", "MRPL36", "MRPL37", "MRPL38", "MRPL39", "MRPL40", 
                   "MRPL41", "MRPL42", "MRPL43", "MRPL44", "MRPL45", "MRPL46", "MRPL47", "MRPL48", 
                   "MRPL49", "MRPL50", "MRPL51", "MRPL52", "MRPL53", "MRPL54", "MRPL55", "MRPL57", 
                   "MRPL58", "MRPS2", "MRPS5", "MRPS6", "MRPS7", "MRPS9", "MRPS10", "MRPS11", 
                   "MRPS12", "MRPS14", "MRPS15", "MRPS16", "MRPS17", "MRPS18A", "MRPS18B", "MRPS18C", 
                   "MRPS21", "MRPS22", "MRPS23", "MRPS24", "MRPS25", "MRPS26", "MRPS27", "MRPS28", 
                   "MRPS30", "MRPS31", "MRPS33", "MRPS34", "MRPS35", "MRPS36"),
  
  "柠檬酸循环" = c("CS", "ACO2", "IDH3A", "IDH3B", "IDH3G", "OGDH", "DLST", "SUCLA2", "SUCLG1", 
                 "SUCLG2", "FH", "MDH2", "IDH2"),
  
  "脂肪酸氧化" = c("CPT1A", "CPT1B", "CPT1C", "CPT2", "ACADS", "ACADM", "ACADL", "ACADVL", "ACAD8", 
                 "ACAD9", "ACAD10", "ACAD11", "ETFA", "ETFB", "ETFDH", "HADHA", "HADHB", "HADH", 
                 "ECHS1", "ECHDC1", "ECHDC2", "ECHDC3"),
  
  "线粒体转运" = c("SLC25A1", "SLC25A3", "SLC25A4", "SLC25A5", "SLC25A6", "SLC25A10", "SLC25A11", 
                 "SLC25A12", "SLC25A13", "SLC25A14", "SLC25A15", "SLC25A16", "SLC25A18", "SLC25A19", 
                 "SLC25A20", "SLC25A21", "SLC25A22", "SLC25A23", "SLC25A24", "SLC25A25", "SLC25A26", 
                 "SLC25A27", "SLC25A28", "SLC25A29", "SLC25A30", "SLC25A31", "SLC25A32", "SLC25A33", 
                 "SLC25A34", "SLC25A35", "SLC25A36", "SLC25A37", "SLC25A38", "SLC25A39", "SLC25A40", 
                 "SLC25A41", "SLC25A42", "SLC25A43", "SLC25A44", "SLC25A45", "SLC25A46", "SLC25A47", 
                 "SLC25A48", "SLC25A51", "SLC25A52", "SLC25A53"),
  
  "线粒体蛋白导入" = c("TOMM5", "TOMM6", "TOMM7", "TOMM20", "TOMM22", "TOMM34", "TOMM40", "TOMM40L", 
                    "TOMM70", "TIMM8A", "TIMM8B", "TIMM9", "TIMM10", "TIMM10B", "TIMM13", "TIMM17A", 
                    "TIMM17B", "TIMM21", "TIMM22", "TIMM23", "TIMM29", "TIMM44", "TIMM50"),
  
  "代谢酶和其他" = c("ACAT1", "ALAS1", "ALAS2", "BCAT2", "GLUD1", "GLUD2", "GOT2", "GPT2", "PRDX3", 
                   "PRDX5", "SOD2", "GSR", "TXN2", "TXNRD2", "ACACA", "ACACB", "FASN", "GAPDH", 
                   "DIABLO", "BAK1", "BAX", "BCL2", "BCL2L1", "BCL2L11", "CASP3", "CASP8", "CASP9")
)

# 为每个基因分配功能类别
assign_function <- function(gene) {
  # 移除磷酸化位点标记
  clean_gene <- str_remove(gene, "_P[STY]\\d+.*$")
  
  for (category in names(mito_classification)) {
    if (clean_gene %in% mito_classification[[category]]) {
      return(category)
    }
  }
  return("其他")
}

cat("开始处理蛋白表达数据...\n")

# 读取并处理所有蛋白数据集
all_protein_data <- list()
for (dataset_id in names(protein_datasets)) {
  cat("正在处理", protein_datasets[[dataset_id]]$name, "数据...\n")
  
  data <- read_tsv(protein_datasets[[dataset_id]]$file, show_col_types = FALSE) %>%
    # 清理基因名（移除磷酸化位点标记）
    mutate(
      Clean_Gene = str_remove(`Correlated Gene`, "_P[STY]\\d+.*$"),
      Dataset = protein_datasets[[dataset_id]]$name,
      Dataset_Type = protein_datasets[[dataset_id]]$type,
      Samples = protein_datasets[[dataset_id]]$samples
    ) %>%
    # 筛选线粒体相关基因或重要的代谢/凋亡相关基因
    filter(Clean_Gene %in% mito_genes | 
           `Correlated Gene` %in% c("ACACA", "ACACB", "FASN", "GAPDH", "DIABLO", "BAK1", "BAX", 
                                   "BCL2", "BCL2L1", "BCL2L11", "CASP3", "CASP8", "CASP9", "GSK3A", "GSK3B")) %>%
    # 只保留显著相关的基因
    filter(`q-Value` < 0.05) %>%
    # 添加功能分类和其他变量
    mutate(
      Function_Category = sapply(Clean_Gene, assign_function),
      Significance = case_when(
        `q-Value` < 0.001 ~ "***",
        `q-Value` < 0.01 ~ "**", 
        `q-Value` < 0.05 ~ "*",
        TRUE ~ ""
      ),
      neg_log_p = -log10(`p-Value`),
      Correlation_Direction = ifelse(`Spearman's Correlation` > 0, "正相关", "负相关"),
      abs_correlation = abs(`Spearman's Correlation`)
    ) %>%
    arrange(desc(abs_correlation))
  
  all_protein_data[[dataset_id]] <- data
}

# 合并所有蛋白数据
combined_protein_data <- bind_rows(all_protein_data)

cat("\n蛋白表达数据处理完成！\n")
cat("总显著相关蛋白数:", nrow(combined_protein_data), "\n")

# 统计每个数据集的基本信息
protein_summary <- combined_protein_data %>%
  group_by(Dataset, Dataset_Type, Samples) %>%
  summarise(
    significant_proteins = n(),
    mean_correlation = round(mean(abs_correlation), 3),
    median_correlation = round(median(abs_correlation), 3),
    max_correlation = round(max(abs_correlation), 3),
    .groups = 'drop'
  )

print("蛋白数据集统计:")
print(protein_summary)

# 为CPTAC数据创建主要气泡图（蛋白质组学数据质量最高）
cptac_data <- all_protein_data[["CPTAC_Protein"]]

# 为标签选择前10个最相关的蛋白
top_proteins <- cptac_data %>%
  slice_head(n = 10) %>%
  pull(`Correlated Gene`)

cptac_processed <- cptac_data %>%
  mutate(
    Function_Category = factor(Function_Category, levels = c(
      "呼吸链复合体I", "呼吸链复合体II", "呼吸链复合体III", "呼吸链复合体IV",
      "ATP合酶复合体V", "柠檬酸循环", "脂肪酸氧化", "线粒体核糖体",
      "线粒体蛋白导入", "线粒体转运", "代谢酶和其他", "其他"
    )),
    alpha_level = case_when(
      Significance == "***" ~ 0.8,
      Significance == "**" ~ 0.7,
      Significance == "*" ~ 0.6,
      TRUE ~ 0.5
    ),
    label = ifelse(`Correlated Gene` %in% top_proteins, `Correlated Gene`, "")
  )

# 创建CPTAC蛋白表达气泡图
protein_bubble_plot <- cptac_processed %>%
  ggplot(aes(x = `Spearman's Correlation`,
             y = Function_Category,
             size = neg_log_p,
             color = Correlation_Direction,
             alpha = alpha_level)) +

  # 添加背景网格
  geom_vline(xintercept = 0, linetype = "solid", color = "grey40", linewidth = 0.8) +
  geom_vline(xintercept = c(-0.3, 0.3), linetype = "dashed", color = "grey60", linewidth = 0.5) +
  geom_vline(xintercept = c(-0.5, 0.5), linetype = "dotted", color = "grey70", linewidth = 0.3) +

  # 添加气泡
  geom_point(stroke = 0.3) +

  # 添加基因标签
  geom_text_repel(
    aes(label = label),
    size = 3.5,
    max.overlaps = 15,
    box.padding = 0.4,
    point.padding = 0.3,
    segment.color = "grey40",
    segment.size = 0.4,
    show.legend = FALSE,
    fontface = "bold"
  ) +

  # 添加显著性标记
  geom_text(
    data = . %>% slice_head(n = 15),
    aes(label = Significance),
    size = 3.5,
    vjust = -1.2,
    hjust = 0.5,
    show.legend = FALSE,
    color = "black",
    fontface = "bold"
  ) +

  # 设置颜色
  scale_color_manual(
    values = c("正相关" = "#D73027", "负相关" = "#4575B4"),
    name = "相关性方向"
  ) +

  # 设置透明度
  scale_alpha_identity() +

  # 设置气泡大小
  scale_size_continuous(
    range = c(2, 16),
    name = "-log₁₀(p-value)",
    breaks = c(5, 10, 15, 20),
    labels = c("5", "10", "15", "20+"),
    guide = guide_legend(
      override.aes = list(alpha = 0.8),
      title.position = "top",
      title.hjust = 0.5
    )
  ) +

  # 设置坐标轴
  scale_x_continuous(
    limits = c(-0.4, 0.8),
    breaks = seq(-0.4, 0.8, 0.2),
    labels = seq(-0.4, 0.8, 0.2),
    expand = c(0.02, 0.02)
  ) +

  # 设置Y轴标签
  scale_y_discrete(
    labels = function(x) str_wrap(x, width = 12)
  ) +

  # 设置主题
  theme_minimal() +
  theme(
    panel.grid.major.y = element_line(color = "grey92", linewidth = 0.6),
    panel.grid.minor = element_blank(),
    panel.grid.major.x = element_line(color = "grey96", linewidth = 0.4),
    panel.background = element_rect(fill = "white", color = NA),
    plot.background = element_rect(fill = "white", color = NA),

    axis.text.y = element_text(size = 11, color = "black", face = "bold"),
    axis.text.x = element_text(size = 11, color = "black"),
    axis.title = element_text(size = 13, color = "black", face = "bold"),
    axis.line.x = element_line(color = "grey30", linewidth = 0.5),

    plot.title = element_text(size = 16, face = "bold", hjust = 0.5, margin = margin(b = 10)),
    plot.subtitle = element_text(size = 12, hjust = 0.5, color = "grey20", margin = margin(b = 15)),
    plot.caption = element_text(size = 10, color = "grey40", hjust = 0, margin = margin(t = 15)),

    legend.position = "bottom",
    legend.box = "horizontal",
    legend.title = element_text(size = 11, face = "bold"),
    legend.text = element_text(size = 10),
    legend.key = element_blank(),
    legend.spacing.x = unit(1, "cm"),

    plot.margin = margin(25, 25, 25, 25)
  ) +

  # 设置标签
  labs(
    title = "TRMT10C与线粒体蛋白的相关性分析",
    subtitle = paste0("数据来源: CPTAC Cell 2021 (n=80) | 显著相关蛋白: ", nrow(cptac_processed), "个"),
    x = "Spearman相关系数",
    y = "线粒体功能分类",
    caption = "气泡大小 = 统计显著性 (-log₁₀ p-value) | 显著性标记: *** p<0.001, ** p<0.01, * p<0.05\n标注蛋白为相关性最强的前10个蛋白"
  )

# 显示图形
print(protein_bubble_plot)

# 保存高质量图形
ggsave("TRMT10C_protein_correlation_bubble_plot.png",
       plot = protein_bubble_plot,
       width = 16, height = 12,
       dpi = 300,
       bg = "white")

ggsave("TRMT10C_protein_correlation_bubble_plot.pdf",
       plot = protein_bubble_plot,
       width = 16, height = 12,
       bg = "white")

# 创建多数据集比较图
comparison_data <- combined_protein_data %>%
  # 只保留在多个数据集中都出现的蛋白
  group_by(Clean_Gene) %>%
  filter(n() >= 2) %>%
  ungroup() %>%
  mutate(
    Dataset_Short = case_when(
      str_detect(Dataset, "CPTAC") ~ "CPTAC",
      str_detect(Dataset, "Firehose") ~ "TCGA-F",
      str_detect(Dataset, "PanCancer") ~ "TCGA-P",
      TRUE ~ Dataset
    )
  )

if(nrow(comparison_data) > 0) {
  comparison_plot <- comparison_data %>%
    ggplot(aes(x = `Spearman's Correlation`,
               y = Clean_Gene,
               size = neg_log_p,
               color = Dataset_Short,
               shape = Dataset_Type)) +

    geom_vline(xintercept = 0, linetype = "dashed", color = "grey50") +
    geom_point(alpha = 0.7, stroke = 0.5) +

    scale_color_manual(
      values = c("CPTAC" = "#E31A1C", "TCGA-F" = "#1F78B4", "TCGA-P" = "#33A02C"),
      name = "数据集"
    ) +

    scale_shape_manual(
      values = c("蛋白质组学" = 16, "RPPA" = 17),
      name = "技术平台"
    ) +

    scale_size_continuous(
      range = c(2, 8),
      name = "-log₁₀(p-value)"
    ) +

    theme_minimal() +
    theme(
      axis.text.y = element_text(size = 9),
      axis.text.x = element_text(size = 10),
      legend.position = "bottom"
    ) +

    labs(
      title = "TRMT10C与蛋白表达相关性 - 多数据集比较",
      x = "Spearman相关系数",
      y = "蛋白质",
      caption = "显示在多个数据集中都显著相关的蛋白质"
    )

  print(comparison_plot)

  ggsave("TRMT10C_protein_multi_dataset_comparison.png",
         plot = comparison_plot,
         width = 14, height = 10,
         dpi = 300,
         bg = "white")
}

# 生成详细统计报告
cat("\n=== TRMT10C与蛋白表达相关性分析报告 ===\n")
cat("分析日期:", Sys.Date(), "\n\n")

# 各数据集统计
cat("1. 数据集概况:\n")
for(i in 1:nrow(protein_summary)) {
  cat("   -", protein_summary$Dataset[i],
      "(", protein_summary$Dataset_Type[i], "):",
      protein_summary$significant_proteins[i], "个显著相关蛋白\n")
  cat("     样本数:", protein_summary$Samples[i],
      ", 平均|相关系数|:", protein_summary$mean_correlation[i],
      ", 最大|相关系数|:", protein_summary$max_correlation[i], "\n")
}

# CPTAC数据详细分析（质量最高）
cat("\n2. CPTAC蛋白质组学数据详细分析:\n")
cptac_stats <- cptac_processed %>%
  group_by(Function_Category) %>%
  summarise(
    count = n(),
    mean_correlation = round(mean(abs_correlation), 3),
    max_correlation = round(max(abs_correlation), 3),
    .groups = 'drop'
  ) %>%
  arrange(desc(count))

for(i in 1:nrow(cptac_stats)) {
  cat("   -", cptac_stats$Function_Category[i], ":",
      cptac_stats$count[i], "个蛋白",
      "(平均|相关系数|:", cptac_stats$mean_correlation[i], ")\n")
}

# 前10个最相关的蛋白
cat("\n3. 前10个最相关的蛋白 (CPTAC数据):\n")
top10_proteins <- cptac_processed %>%
  slice_head(n = 10) %>%
  select(`Correlated Gene`, Function_Category, `Spearman's Correlation`, `q-Value`)

for(i in 1:nrow(top10_proteins)) {
  cat("   ", i, ".", top10_proteins$`Correlated Gene`[i],
      "(", top10_proteins$Function_Category[i], ")",
      "- 相关系数:", round(top10_proteins$`Spearman's Correlation`[i], 3),
      ", q-value:", format(top10_proteins$`q-Value`[i], scientific = TRUE, digits = 2), "\n")
}

# 保存详细结果
write_csv(combined_protein_data, "TRMT10C_protein_correlation_results.csv")
write_csv(cptac_processed, "TRMT10C_CPTAC_protein_results.csv")

cat("\n详细结果已保存:\n")
cat("- TRMT10C_protein_correlation_results.csv (所有数据集)\n")
cat("- TRMT10C_CPTAC_protein_results.csv (CPTAC数据)\n")
cat("- TRMT10C_protein_correlation_bubble_plot.png/pdf\n")
if(nrow(comparison_data) > 0) {
  cat("- TRMT10C_protein_multi_dataset_comparison.png\n")
}

cat("\n=== 蛋白表达分析完成 ===\n")
