# TRMT10C与线粒体基因相关性 - 多数据集比较分析
# 作者: AI Assistant
# 日期: 2025-07-08

# 加载必要的包
library(tidyverse)
library(ggplot2)
library(dplyr)
library(readr)
library(patchwork)
library(corrplot)

# 设置工作目录
setwd(".")

# 定义数据集信息
datasets <- list(
  "TCGA_GDC" = list(
    file = "TCGA_GDC_503 Samples/mRNATPM vs TRMT10CmRNA (TCGA, GDC).tsv",
    name = "TCGA GDC",
    samples = 503
  ),
  "TCGA_Firehose" = list(
    file = "TCGA_Firehose Legacy_511 Samples/mRNA vs TRMT10CmRNA (TCGA, Firehose Legacy).tsv",
    name = "TCGA Firehose",
    samples = 511
  ),
  "TCGA_Nature" = list(
    file = "TCGA_Nature 2012 178 Samples/mRNARPKM vs TRMT10CmRNA (TCGA, Nature 2012).tsv",
    name = "TCGA Nature 2012",
    samples = 178
  ),
  "TCGA_PanCancer" = list(
    file = "TCGA_PanCancer Atlas 487 Samples/mRNARSEM vs TRMT10CmRNA (TCGA, PanCancer Atlas).tsv",
    name = "TCGA PanCancer",
    samples = 487
  ),
  "CPTAC_GDC" = list(
    file = "CPTAC_GDC_90 Samples/mRNATPM vs TRMT10CmRNA (CPTAC, GDC).tsv",
    name = "CPTAC GDC",
    samples = 90
  ),
  "CPTAC_Cell" = list(
    file = "CPTAC_Cell 2021_80 Samples/mRNAFPKM vs TRMT10CmRNAFPKM (CPTAC, Cell 2021).tsv",
    name = "CPTAC Cell 2021",
    samples = 80
  )
)

# 读取线粒体基因列表
mito_genes <- read_lines("能量代谢相关基因.txt")
mito_genes <- mito_genes[mito_genes != "" & mito_genes != "Symbol"]

# 读取所有数据集
all_data <- list()
for (dataset_id in names(datasets)) {
  cat("正在读取", datasets[[dataset_id]]$name, "数据...\n")
  
  data <- read_tsv(datasets[[dataset_id]]$file, show_col_types = FALSE) %>%
    filter(`Correlated Gene` %in% mito_genes) %>%
    filter(`q-Value` < 0.05) %>%
    mutate(
      Dataset = datasets[[dataset_id]]$name,
      Dataset_ID = dataset_id,
      Samples = datasets[[dataset_id]]$samples,
      abs_correlation = abs(`Spearman's Correlation`)
    ) %>%
    select(`Correlated Gene`, `Spearman's Correlation`, `p-Value`, `q-Value`, 
           Dataset, Dataset_ID, Samples, abs_correlation)
  
  all_data[[dataset_id]] <- data
}

# 合并所有数据
combined_data <- bind_rows(all_data)

# 统计每个数据集的基本信息
dataset_summary <- combined_data %>%
  group_by(Dataset, Samples) %>%
  summarise(
    significant_genes = n(),
    mean_correlation = round(mean(abs_correlation), 3),
    median_correlation = round(median(abs_correlation), 3),
    max_correlation = round(max(abs_correlation), 3),
    .groups = 'drop'
  ) %>%
  arrange(desc(significant_genes))

cat("\n=== 多数据集比较分析结果 ===\n")
print(dataset_summary)

# 找到在所有数据集中都显著的基因
consistent_genes <- combined_data %>%
  group_by(`Correlated Gene`) %>%
  summarise(
    datasets_count = n_distinct(Dataset),
    mean_correlation = mean(`Spearman's Correlation`),
    sd_correlation = sd(`Spearman's Correlation`),
    .groups = 'drop'
  ) %>%
  filter(datasets_count >= 4) %>%  # 至少在4个数据集中显著
  arrange(desc(datasets_count), desc(abs(mean_correlation)))

cat("\n在多个数据集中一致显著的基因 (≥4个数据集):\n")
print(head(consistent_genes, 20))

# 创建相关系数比较热图
correlation_matrix <- combined_data %>%
  select(`Correlated Gene`, Dataset, `Spearman's Correlation`) %>%
  pivot_wider(names_from = Dataset, values_from = `Spearman's Correlation`) %>%
  column_to_rownames("Correlated Gene") %>%
  as.matrix()

# 只保留在至少3个数据集中都有数据的基因
genes_with_data <- rowSums(!is.na(correlation_matrix)) >= 3
correlation_matrix_filtered <- correlation_matrix[genes_with_data, ]

# 创建热图
png("TRMT10C_multi_dataset_heatmap.png", width = 12, height = 16, units = "in", res = 300)
corrplot(correlation_matrix_filtered[1:min(50, nrow(correlation_matrix_filtered)), ],
         method = "color",
         col = colorRampPalette(c("#4575B4", "white", "#D73027"))(100),
         is.corr = FALSE,
         na.label = "square",
         na.col = "grey90",
         tl.cex = 0.8,
         tl.col = "black",
         cl.cex = 0.8,
         title = "TRMT10C与线粒体基因相关性 - 多数据集比较\n(前50个一致性基因)",
         mar = c(0, 0, 2, 0))
dev.off()

cat("\n热图已保存: TRMT10C_multi_dataset_heatmap.png\n")

# 保存详细结果
write_csv(combined_data, "TRMT10C_multi_dataset_results.csv")
write_csv(consistent_genes, "TRMT10C_consistent_genes.csv")
write_csv(dataset_summary, "TRMT10C_dataset_summary.csv")

cat("\n详细结果已保存:\n")
cat("- TRMT10C_multi_dataset_results.csv\n")
cat("- TRMT10C_consistent_genes.csv\n") 
cat("- TRMT10C_dataset_summary.csv\n")

cat("\n=== 分析完成 ===\n")
